---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/upgrade.cattle.io/plan_v1.json
apiVersion: upgrade.cattle.io/v1
kind: Plan
metadata:
  name: kubernetes
spec:
  version: ${KUBERNETES_VERSION}
  concurrency: 1
  exclusive: true
  jobActiveDeadlineSecs: 1800  # 30 minutes for Kubernetes upgrades
  serviceAccountName: system-upgrade-controller
  secrets:
    - name: system-upgrade-controller
      path: /var/run/secrets/talos.dev
      ignoreUpdates: true
  nodeSelector:
    matchExpressions:
      - key: node-role.kubernetes.io/control-plane
        operator: Exists
  upgrade:
    image: "ghcr.io/siderolabs/talosctl:${TALOS_VERSION}"
    args:
      - "--nodes=$(SYSTEM_UPGRADE_NODE_NAME)"
      - "upgrade-k8s"
      - "--to=${KUBERNETES_VERSION}"