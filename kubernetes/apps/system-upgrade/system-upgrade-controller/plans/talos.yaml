---
# yaml-language-server: $schema=https://kubernetes-schemas.pages.dev/upgrade.cattle.io/plan_v1.json
apiVersion: upgrade.cattle.io/v1
kind: Plan
metadata:
  name: talos
spec:
  version: ${TALOS_VERSION}
  concurrency: 1
  postCompleteDelay: 2m
  exclusive: true
  jobActiveDeadlineSecs: 2400  # 40 minutes for Talos upgrades (includes reboot time)
  serviceAccountName: system-upgrade-controller
  secrets:
    - name: system-upgrade-controller
      path: /var/run/secrets/talos.dev
      ignoreUpdates: true
  nodeSelector:
    matchExpressions:
      - key: kubernetes.io/hostname
        operator: Exists
  upgrade:
    image: "ghcr.io/jfroy/tnu:0.4.3"
    args:
      - "--node=$(SYSTEM_UPGRADE_NODE_NAME)"
      - "--tag=${TALOS_VERSION}"
      - "--powercycle"